<x-app-layout>
  <x-slot name="header">
    <div class="flex items-center justify-between">
      <h2 class="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
        {{ __('User Management') }}
      </h2>
      <a class="rounded bg-blue-500 px-4 py-2 font-bold text-white hover:bg-blue-700" href="{{ route('admin.users.create') }}">
        Add New User
      </a>
    </div>
  </x-slot>

  <div class="py-12">
    <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
      <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg dark:bg-gray-800">
        <div class="p-6 text-gray-900 dark:text-gray-100">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">Name</th>
                  <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">Email</th>
                  <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">Role</th>
                  <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">Actions</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
                @foreach ($users as $user)
                  <tr>
                    <td class="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-gray-100">
                      {{ $user->name }}
                    </td>
                    <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                      {{ $user->email }}
                    </td>
                    <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                      <span
                            class="@if ($user->role === 'admin') bg-red-100 text-red-800 
                                            @elseif($user->role === 'editor') bg-yellow-100 text-yellow-800 
                                            @else bg-green-100 text-green-800 @endif inline-flex rounded-full px-2 text-xs font-semibold leading-5">
                        {{ ucfirst($user->role) }}
                      </span>
                    </td>
                    <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                      @if ($user->isApproved())
                        <span class="inline-flex rounded-full bg-green-100 px-2 text-xs font-semibold leading-5 text-green-800">
                          Approved
                        </span>
                      @else
                        <span class="inline-flex rounded-full bg-red-100 px-2 text-xs font-semibold leading-5 text-red-800">
                          Pending
                        </span>
                      @endif
                    </td>
                    <td class="space-x-2 whitespace-nowrap px-6 py-4 text-sm font-medium">
                      <a class="text-indigo-600 hover:text-indigo-900" href="{{ route('admin.users.edit', $user) }}">Edit</a>

                      @if (!$user->isApproved())
                        <form action="{{ route('admin.users.approve', $user) }}" class="inline" method="POST">
                          @csrf
                          <button class="text-green-600 hover:text-green-900" type="submit">Approve</button>
                        </form>
                      @else
                        <form action="{{ route('admin.users.reject', $user) }}" class="inline" method="POST">
                          @csrf
                          <button class="text-yellow-600 hover:text-yellow-900" type="submit">Revoke</button>
                        </form>
                      @endif

                      @if ($user->id !== auth()->id())
                        <form action="{{ route('admin.users.destroy', $user) }}" class="inline" method="POST"
                              onsubmit="return confirm('Are you sure you want to delete this user?')">
                          @csrf
                          @method('DELETE')
                          <button class="text-red-600 hover:text-red-900" type="submit">Delete</button>
                        </form>
                      @endif
                    </td>
                  </tr>
                @endforeach
              </tbody>
            </table>
          </div>

          <div class="mt-4">
            {{ $users->links() }}
          </div>
        </div>
      </div>
    </div>
  </div>
</x-app-layout>
