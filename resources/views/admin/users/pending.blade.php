<x-app-layout>
  <x-slot name="header">
    <div class="flex items-center justify-between">
      <h2 class="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
        {{ __('Pending User Approvals') }}
      </h2>
      <div class="flex space-x-2">
        <a class="rounded bg-gray-500 px-4 py-2 font-bold text-white hover:bg-gray-700" href="{{ route('admin.users.index') }}">
          All Users
        </a>
        <a class="rounded bg-blue-500 px-4 py-2 font-bold text-white hover:bg-blue-700" href="{{ route('admin.users.create') }}">
          Add New User
        </a>
      </div>
    </div>
  </x-slot>

  <div class="py-12">
    <div class="mx-auto max-w-7xl sm:px-6 lg:px-8 space-y-6">
      
      <!-- Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-8 w-8 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Pending Approval</p>
                <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">{{ $pendingUsers->total() }}</p>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-8 w-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 8a2 2 0 100-4 2 2 0 000 4zm0 0v4a2 2 0 002 2h4a2 2 0 002-2v-4" />
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">This Week</p>
                <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">{{ \App\Models\User::where('is_approved', false)->where('created_at', '>=', now()->subWeek())->count() }}</p>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-8 w-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Approved Today</p>
                <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">{{ \App\Models\User::where('is_approved', true)->whereDate('approved_at', today())->count() }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      @if($pendingUsers->count() > 0)
      <!-- Bulk Actions -->
      <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6">
          <form id="bulk-action-form" method="POST">
            @csrf
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <label class="flex items-center">
                  <input type="checkbox" id="select-all" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                  <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Select All</span>
                </label>
                <select name="bulk_action" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                  <option value="">Bulk Actions</option>
                  <option value="approve">Approve Selected</option>
                  <option value="delete">Delete Selected</option>
                </select>
              </div>
              <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Apply
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Pending Users List -->
      <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg dark:bg-gray-800">
        <div class="p-6 text-gray-900 dark:text-gray-100">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm">
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">User</th>
                  <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">Role</th>
                  <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">Registered</th>
                  <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">Actions</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
                @foreach ($pendingUsers as $user)
                  <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <input type="checkbox" name="selected_users[]" value="{{ $user->id }}" class="user-checkbox rounded border-gray-300 text-blue-600 shadow-sm">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10">
                          <div class="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                              {{ strtoupper(substr($user->name, 0, 2)) }}
                            </span>
                          </div>
                        </div>
                        <div class="ml-4">
                          <div class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ $user->name }}</div>
                          <div class="text-sm text-gray-500 dark:text-gray-400">{{ $user->email }}</div>
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex rounded-full px-2 text-xs font-semibold leading-5 
                        @if($user->role === 'admin') bg-red-100 text-red-800
                        @elseif($user->role === 'editor') bg-yellow-100 text-yellow-800
                        @else bg-green-100 text-green-800 @endif">
                        {{ ucfirst($user->role) }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      <div>{{ $user->created_at->format('M d, Y') }}</div>
                      <div class="text-xs text-gray-400">{{ $user->created_at->diffForHumans() }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <form action="{{ route('admin.users.approve', $user) }}" class="inline" method="POST">
                        @csrf
                        <button class="text-green-600 hover:text-green-900 bg-green-100 hover:bg-green-200 px-3 py-1 rounded" type="submit">
                          Approve
                        </button>
                      </form>
                      
                      <a href="{{ route('admin.users.show', $user) }}" class="text-blue-600 hover:text-blue-900 bg-blue-100 hover:bg-blue-200 px-3 py-1 rounded">
                        Review
                      </a>
                      
                      <form action="{{ route('admin.users.destroy', $user) }}" class="inline" method="POST"
                            onsubmit="return confirm('Are you sure you want to delete this user?')">
                        @csrf
                        @method('DELETE')
                        <button class="text-red-600 hover:text-red-900 bg-red-100 hover:bg-red-200 px-3 py-1 rounded" type="submit">
                          Delete
                        </button>
                      </form>
                    </td>
                  </tr>
                @endforeach
              </tbody>
            </table>
          </div>

          <div class="mt-4">
            {{ $pendingUsers->links() }}
          </div>
        </div>
      </div>
      @else
      <!-- No Pending Users -->
      <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6 text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No pending approvals</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">All users have been reviewed and approved.</p>
        </div>
      </div>
      @endif
    </div>
  </div>

  <script>
    // Select all functionality
    document.getElementById('select-all').addEventListener('change', function() {
      const checkboxes = document.querySelectorAll('.user-checkbox');
      checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
      });
    });

    // Bulk action form submission
    document.getElementById('bulk-action-form').addEventListener('submit', function(e) {
      const selectedUsers = document.querySelectorAll('.user-checkbox:checked');
      const bulkAction = document.querySelector('select[name="bulk_action"]').value;
      
      if (selectedUsers.length === 0) {
        e.preventDefault();
        alert('Please select at least one user.');
        return;
      }
      
      if (!bulkAction) {
        e.preventDefault();
        alert('Please select an action.');
        return;
      }
      
      if (bulkAction === 'delete') {
        if (!confirm('Are you sure you want to delete the selected users? This action cannot be undone.')) {
          e.preventDefault();
          return;
        }
      }
      
      // Set the form action based on bulk action
      if (bulkAction === 'approve') {
        this.action = '{{ route("admin.users.bulk-approve") }}';
      } else if (bulkAction === 'delete') {
        this.action = '{{ route("admin.users.bulk-delete") }}';
      }
    });
  </script>
</x-app-layout>
