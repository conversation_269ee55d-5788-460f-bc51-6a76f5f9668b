<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
  <meta charset="utf-8">
  <meta
    content="width=device-width, initial-scale=1"
    name="viewport"
  >
  <meta
    content="{{ csrf_token() }}"
    name="csrf-token"
  >

  <title>{{ config('app.name', 'Laravel') }}</title>

  <!-- Fonts -->
  <link
    href="https://fonts.bunny.net"
    rel="preconnect"
  >
  <link
    href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap"
    rel="stylesheet"
  />

  <!-- Scripts -->
  @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

<body class="font-sans text-gray-900 antialiased">
  <!-- Page Heading -->
  @if (isset($header))
    <header class="bg-white shadow dark:bg-gray-800 w-full sm:fixed sm:top-0">
        {{ $header }}
    </header>
  @endif
  <div class="flex min-h-screen flex-col items-center bg-gray-100 pt-6 sm:justify-center sm:pt-0 dark:bg-gray-900">
    <div>
      <a href="/">
        <x-application-logo class="h-20 w-20 fill-current text-gray-500" />
      </a>
    </div>

    <div class="w-full sm:px-4">
        <div class="mt-6 overflow-hidden bg-white px-6 py-4 shadow-md sm:rounded-lg sm:mx-auto sm:max-w-5xl dark:bg-gray-800">
            {{ $slot }}
        </div>
    </div>
  </div>
</body>

</html>
