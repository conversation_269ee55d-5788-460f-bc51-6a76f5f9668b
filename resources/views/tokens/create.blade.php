<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ __('Create API Token') }}
            </h2>
            <a href="{{ route('tokens.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Tokens
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-2xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <form method="POST" action="{{ route('tokens.store') }}">
                        @csrf

                        <!-- Token Name -->
                        <div class="mb-6">
                            <x-input-label for="name" :value="__('Token Name')" />
                            <x-text-input id="name" class="block mt-1 w-full" type="text" name="name" :value="old('name')" required autofocus placeholder="e.g., Mobile App, Website Integration" />
                            <x-input-error :messages="$errors->get('name')" class="mt-2" />
                            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                                Choose a descriptive name to help you identify this token later.
                            </p>
                        </div>

                        <!-- Token Abilities -->
                        <div class="mb-6">
                            <x-input-label :value="__('Token Abilities')" />
                            <div class="mt-2 space-y-3">
                                <div class="flex items-start">
                                    <div class="flex items-center h-5">
                                        <input id="ability_read" name="abilities[]" type="checkbox" value="read" checked disabled
                                               class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="ability_read" class="font-medium text-gray-700 dark:text-gray-300">Read</label>
                                        <p class="text-gray-500 dark:text-gray-400">Access to read data from the API (always included)</p>
                                    </div>
                                </div>

                                @if(auth()->user()->hasWriteAccess())
                                <div class="flex items-start">
                                    <div class="flex items-center h-5">
                                        <input id="ability_write" name="abilities[]" type="checkbox" value="write" checked
                                               class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="ability_write" class="font-medium text-gray-700 dark:text-gray-300">Write</label>
                                        <p class="text-gray-500 dark:text-gray-400">Ability to create and update data via the API</p>
                                    </div>
                                </div>
                                @endif

                                @if(auth()->user()->isAdmin())
                                <div class="flex items-start">
                                    <div class="flex items-center h-5">
                                        <input id="ability_admin" name="abilities[]" type="checkbox" value="admin"
                                               class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="ability_admin" class="font-medium text-gray-700 dark:text-gray-300">Admin</label>
                                        <p class="text-gray-500 dark:text-gray-400">Full administrative access to the API</p>
                                    </div>
                                </div>
                                @endif
                            </div>
                            <x-input-error :messages="$errors->get('abilities')" class="mt-2" />
                        </div>

                        <!-- Security Notice -->
                        <div class="mb-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                                        Security Notice
                                    </h3>
                                    <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                                        <ul class="list-disc list-inside space-y-1">
                                            <li>Your token will be displayed only once after creation</li>
                                            <li>Store your token securely and never share it publicly</li>
                                            <li>If you lose your token, you'll need to create a new one</li>
                                            <li>Revoke tokens that are no longer needed</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center justify-end space-x-3">
                            <a href="{{ route('tokens.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <x-primary-button>
                                {{ __('Create Token') }}
                            </x-primary-button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Available Endpoints -->
            <div class="mt-6 bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">Available API Endpoints</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                            <div>
                                <span class="font-mono text-sm bg-green-100 text-green-800 px-2 py-1 rounded">GET</span>
                                <span class="ml-2 font-mono text-sm">/api/stories</span>
                            </div>
                            <span class="text-sm text-gray-500">Get all stories</span>
                        </div>
                        <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                            <div>
                                <span class="font-mono text-sm bg-green-100 text-green-800 px-2 py-1 rounded">GET</span>
                                <span class="ml-2 font-mono text-sm">/api/stories/{id}</span>
                            </div>
                            <span class="text-sm text-gray-500">Get specific story</span>
                        </div>
                        @if(auth()->user()->hasWriteAccess())
                        <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                            <div>
                                <span class="font-mono text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">POST</span>
                                <span class="ml-2 font-mono text-sm">/api/sanity/article/upsert</span>
                            </div>
                            <span class="text-sm text-gray-500">Create/update article</span>
                        </div>
                        <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                            <div>
                                <span class="font-mono text-sm bg-red-100 text-red-800 px-2 py-1 rounded">POST</span>
                                <span class="ml-2 font-mono text-sm">/api/sanity/article/delete</span>
                            </div>
                            <span class="text-sm text-gray-500">Delete article</span>
                        </div>
                        @endif
                    </div>
                    <div class="mt-4">
                        <a href="{{ route('api.docs') }}" class="text-blue-600 hover:text-blue-500 text-sm font-medium">
                            View full API documentation →
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
