<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ __('API Token: ') . $token->name }}
            </h2>
            <a href="{{ route('tokens.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Tokens
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8 space-y-6">
            
            @if($plainTextToken)
            <!-- Token Display (only shown once) -->
            <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3 flex-1">
                        <h3 class="text-sm font-medium text-green-800 dark:text-green-200">
                            Token Created Successfully!
                        </h3>
                        <div class="mt-2 text-sm text-green-700 dark:text-green-300">
                            <p class="mb-3">Your API token has been created. <strong>Copy it now as you won't be able to see it again!</strong></p>
                            <div class="bg-white dark:bg-gray-800 border border-green-300 dark:border-green-600 rounded-md p-3">
                                <div class="flex items-center justify-between">
                                    <code id="token-value" class="text-sm font-mono text-gray-900 dark:text-gray-100 break-all">{{ $plainTextToken }}</code>
                                    <button onclick="copyToken()" class="ml-3 flex-shrink-0 bg-green-600 hover:bg-green-700 text-white text-xs px-3 py-1 rounded">
                                        Copy
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Token Details -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">Token Details</h3>
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $token->name }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $token->created_at->format('M d, Y \a\t g:i A') }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Last Used</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                @if($token->last_used_at)
                                    {{ $token->last_used_at->format('M d, Y \a\t g:i A') }}
                                    <span class="text-gray-500">({{ $token->last_used_at->diffForHumans() }})</span>
                                @else
                                    <span class="text-gray-500">Never used</span>
                                @endif
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Abilities</dt>
                            <dd class="mt-1">
                                @if($token->abilities && count($token->abilities) > 0)
                                    <div class="flex flex-wrap gap-1">
                                        @foreach($token->abilities as $ability)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                                @if($ability === 'admin') bg-red-100 text-red-800
                                                @elseif($ability === 'write') bg-yellow-100 text-yellow-800
                                                @else bg-green-100 text-green-800 @endif">
                                                {{ ucfirst($ability) }}
                                            </span>
                                        @endforeach
                                    </div>
                                @else
                                    <span class="text-sm text-gray-500">All abilities</span>
                                @endif
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>

            <!-- Usage Examples -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">Usage Examples</h3>
                    <div class="space-y-6">
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">cURL</h4>
                            <div class="bg-gray-100 dark:bg-gray-700 rounded-md p-4">
                                <code class="text-sm font-mono text-gray-900 dark:text-gray-100">
                                    curl -H "Authorization: Bearer YOUR_TOKEN_HERE" \<br>
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-H "Accept: application/json" \<br>
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{ url('/api/stories') }}
                                </code>
                            </div>
                        </div>

                        <div>
                            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">JavaScript (Fetch API)</h4>
                            <div class="bg-gray-100 dark:bg-gray-700 rounded-md p-4">
                                <code class="text-sm font-mono text-gray-900 dark:text-gray-100">
                                    fetch('{{ url('/api/stories') }}', {<br>
                                    &nbsp;&nbsp;method: 'GET',<br>
                                    &nbsp;&nbsp;headers: {<br>
                                    &nbsp;&nbsp;&nbsp;&nbsp;'Authorization': 'Bearer YOUR_TOKEN_HERE',<br>
                                    &nbsp;&nbsp;&nbsp;&nbsp;'Accept': 'application/json',<br>
                                    &nbsp;&nbsp;&nbsp;&nbsp;'Content-Type': 'application/json'<br>
                                    &nbsp;&nbsp;}<br>
                                    })<br>
                                    .then(response => response.json())<br>
                                    .then(data => console.log(data));
                                </code>
                            </div>
                        </div>

                        <div>
                            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Python (Requests)</h4>
                            <div class="bg-gray-100 dark:bg-gray-700 rounded-md p-4">
                                <code class="text-sm font-mono text-gray-900 dark:text-gray-100">
                                    import requests<br><br>
                                    headers = {<br>
                                    &nbsp;&nbsp;&nbsp;&nbsp;'Authorization': 'Bearer YOUR_TOKEN_HERE',<br>
                                    &nbsp;&nbsp;&nbsp;&nbsp;'Accept': 'application/json'<br>
                                    }<br><br>
                                    response = requests.get('{{ url('/api/stories') }}', headers=headers)<br>
                                    data = response.json()
                                </code>
                            </div>
                        </div>

                        <div>
                            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">PHP (Guzzle)</h4>
                            <div class="bg-gray-100 dark:bg-gray-700 rounded-md p-4">
                                <code class="text-sm font-mono text-gray-900 dark:text-gray-100">
                                    use GuzzleHttp\Client;<br><br>
                                    $client = new Client();<br>
                                    $response = $client->get('{{ url('/api/stories') }}', [<br>
                                    &nbsp;&nbsp;&nbsp;&nbsp;'headers' => [<br>
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'Authorization' => 'Bearer YOUR_TOKEN_HERE',<br>
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'Accept' => 'application/json'<br>
                                    &nbsp;&nbsp;&nbsp;&nbsp;]<br>
                                    ]);<br>
                                    $data = json_decode($response->getBody(), true);
                                </code>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">Actions</h3>
                    <div class="flex space-x-3">
                        <a href="{{ route('api.docs') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            View API Documentation
                        </a>
                        <form action="{{ route('tokens.destroy', $token) }}" method="POST" class="inline"
                              onsubmit="return confirm('Are you sure you want to revoke this token? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                Revoke Token
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if($plainTextToken)
    <script>
        function copyToken() {
            const tokenValue = document.getElementById('token-value').textContent;
            navigator.clipboard.writeText(tokenValue).then(function() {
                // Change button text temporarily
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.classList.remove('bg-green-600', 'hover:bg-green-700');
                button.classList.add('bg-green-800');
                
                setTimeout(function() {
                    button.textContent = originalText;
                    button.classList.remove('bg-green-800');
                    button.classList.add('bg-green-600', 'hover:bg-green-700');
                }, 2000);
            });
        }
    </script>
    @endif
</x-app-layout>
