<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ __('API Documentation') }}
            </h2>
            <a href="{{ route('tokens.index') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Manage Tokens
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8 space-y-6">
            
            <!-- Introduction -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">Getting Started</h3>
                    <div class="prose dark:prose-invert max-w-none">
                        <p>Welcome to the CNA API! This API allows you to access and interact with our content management system programmatically.</p>
                        
                        <h4 class="text-base font-medium mt-6 mb-3">Base URL</h4>
                        <div class="bg-gray-100 dark:bg-gray-700 rounded-md p-3 font-mono text-sm">
                            {{ url('/api') }}
                        </div>

                        <h4 class="text-base font-medium mt-6 mb-3">Authentication</h4>
                        <p>All API requests require authentication using Bearer tokens. Include your token in the Authorization header:</p>
                        <div class="bg-gray-100 dark:bg-gray-700 rounded-md p-3 font-mono text-sm">
                            Authorization: Bearer YOUR_TOKEN_HERE
                        </div>
                    </div>
                </div>
            </div>

            <!-- Available Endpoints -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">Available Endpoints</h3>
                    
                    <!-- Stories Endpoints -->
                    <div class="mb-8">
                        <h4 class="text-base font-medium mb-4 text-blue-600 dark:text-blue-400">Stories</h4>
                        <div class="space-y-4">
                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">GET</span>
                                    <span class="ml-3 font-mono text-sm">/api/stories</span>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Retrieve a list of all stories.</p>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded p-3">
                                    <p class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Example Response:</p>
                                    <pre class="text-xs text-gray-600 dark:text-gray-400"><code>{
  "data": [
    {
      "id": 1,
      "title": "Sample Story",
      "content": "Story content...",
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}</code></pre>
                                </div>
                            </div>

                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">GET</span>
                                    <span class="ml-3 font-mono text-sm">/api/stories/{id}</span>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Retrieve a specific story by ID.</p>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded p-3">
                                    <p class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Example Request:</p>
                                    <pre class="text-xs text-gray-600 dark:text-gray-400"><code>GET /api/stories/1</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if(auth()->user()->hasWriteAccess())
                    <!-- Sanity/Article Endpoints -->
                    <div class="mb-8">
                        <h4 class="text-base font-medium mb-4 text-purple-600 dark:text-purple-400">Content Management</h4>
                        <div class="space-y-4">
                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">POST</span>
                                    <span class="ml-3 font-mono text-sm">/api/sanity/article/upsert</span>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Create or update an article in the content management system.</p>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded p-3">
                                    <p class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Required Abilities:</p>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Write</span>
                                </div>
                            </div>

                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">POST</span>
                                    <span class="ml-3 font-mono text-sm">/api/sanity/article/delete</span>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Delete an article from the content management system.</p>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded p-3">
                                    <p class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Required Abilities:</p>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Write</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- User Endpoints -->
                    <div class="mb-8">
                        <h4 class="text-base font-medium mb-4 text-orange-600 dark:text-orange-400">User Information</h4>
                        <div class="space-y-4">
                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">GET</span>
                                    <span class="ml-3 font-mono text-sm">/api/user</span>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Get information about the authenticated user.</p>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded p-3">
                                    <p class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Example Response:</p>
                                    <pre class="text-xs text-gray-600 dark:text-gray-400"><code>{
  "id": 1,
  "name": "John Doe",
  "email": "<EMAIL>",
  "role": "user"
}</code></pre>
                                </div>
                            </div>

                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">POST</span>
                                    <span class="ml-3 font-mono text-sm">/api/tokens/create</span>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Create a new API token programmatically.</p>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded p-3">
                                    <p class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Request Body:</p>
                                    <pre class="text-xs text-gray-600 dark:text-gray-400"><code>{
  "token_name": "My App Token"
}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Handling -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">Error Handling</h3>
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-sm font-medium mb-2">HTTP Status Codes</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="bg-gray-50 dark:bg-gray-700 rounded p-3">
                                    <div class="flex justify-between items-center">
                                        <span class="font-mono text-sm">200</span>
                                        <span class="text-sm">Success</span>
                                    </div>
                                </div>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded p-3">
                                    <div class="flex justify-between items-center">
                                        <span class="font-mono text-sm">401</span>
                                        <span class="text-sm">Unauthorized</span>
                                    </div>
                                </div>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded p-3">
                                    <div class="flex justify-between items-center">
                                        <span class="font-mono text-sm">403</span>
                                        <span class="text-sm">Forbidden</span>
                                    </div>
                                </div>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded p-3">
                                    <div class="flex justify-between items-center">
                                        <span class="font-mono text-sm">404</span>
                                        <span class="text-sm">Not Found</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h4 class="text-sm font-medium mb-2">Error Response Format</h4>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded p-3">
                                <pre class="text-xs text-gray-600 dark:text-gray-400"><code>{
  "message": "Unauthenticated.",
  "error": "authentication_required"
}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rate Limiting -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">Rate Limiting</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                        API requests are limited to 60 requests per minute per IP address. Rate limit information is included in response headers:
                    </p>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded p-3">
                        <pre class="text-xs text-gray-600 dark:text-gray-400"><code>X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1640995200</code></pre>
                    </div>
                </div>
            </div>

            <!-- Support -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">Support</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        If you need help with the API or have questions about your integration, please contact our support team or check the documentation for more detailed examples.
                    </p>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
