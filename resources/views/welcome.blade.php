<x-guest-layout>
  <x-slot name="header">
    @if (Route::has('login'))
      <div class="z-10 p-6 text-right sm:right-0 sm:top-0">
        @auth
          <a
            class="font-semibold text-gray-600 hover:text-gray-900 focus:rounded-sm focus:outline focus:outline-2 focus:outline-red-500 dark:text-gray-400 dark:hover:text-white"
            href="{{ url('/dashboard') }}"
          >Dashboard</a>
        @else
          <a
            class="font-semibold text-gray-600 hover:text-gray-900 focus:rounded-sm focus:outline focus:outline-2 focus:outline-red-500 dark:text-gray-400 dark:hover:text-white"
            href="{{ route('login') }}"
          >Log in</a>

          @if (Route::has('register'))
            <a
              class="ml-4 font-semibold text-gray-600 hover:text-gray-900 focus:rounded-sm focus:outline focus:outline-2 focus:outline-red-500 dark:text-gray-400 dark:hover:text-white"
              href="{{ route('register') }}"
            >Register</a>
          @endif
        @endauth
      </div>
    @endif
  </x-slot>
  <div class="max-w-4xl mx-auto px-6 py-12">
    <div class="text-center mb-12">
      <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
        CNA API
      </h1>
      <p class="text-xl text-gray-600 dark:text-gray-300">
        Catholic News Agency Content Management System
      </p>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mb-8">
      <h2 class="text-2xl font-semibold text-gray-900 dark:text-white mb-6">About This Application</h2>

      <div class="prose dark:prose-invert max-w-none">
        <p class="text-gray-700 dark:text-gray-300 mb-4">
          The CNA API is a comprehensive content management system designed specifically for Catholic News Agency.
          This Laravel-based application serves as the backend infrastructure for managing and distributing news content,
          stories, and multimedia resources across various platforms.
        </p>

        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mt-6 mb-3">Key Features</h3>
        <ul class="list-disc list-inside text-gray-700 dark:text-gray-300 space-y-2">
          <li><strong>Story Management:</strong> Complete CRUD operations for news stories with rich content support</li>
          <li><strong>Author Management:</strong> Comprehensive author profiles with social media integration</li>
          <li><strong>Content Organization:</strong> Hierarchical categorization with sections, tags, and taxonomies</li>
          <li><strong>Geographic Tagging:</strong> Location-based content organization by countries and cities</li>
          <li><strong>Media Integration:</strong> Support for images, videos, and multimedia content with metadata</li>
          <li><strong>Sanity CMS Integration:</strong> Seamless content synchronization with Sanity headless CMS</li>
          <li><strong>Search Functionality:</strong> Full-text search capabilities across titles and content</li>
          <li><strong>API-First Architecture:</strong> RESTful API design for frontend and mobile applications</li>
        </ul>

        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mt-6 mb-3">Content Types</h3>
        <div class="grid md:grid-cols-3 gap-4 mt-4">
          <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h4 class="font-semibold text-blue-900 dark:text-blue-100">News Stories</h4>
            <p class="text-sm text-blue-700 dark:text-blue-300">Breaking news, feature articles, and investigative reports</p>
          </div>
          <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <h4 class="font-semibold text-green-900 dark:text-green-100">Resources</h4>
            <p class="text-sm text-green-700 dark:text-green-300">Educational materials, documents, and reference content</p>
          </div>
          <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
            <h4 class="font-semibold text-purple-900 dark:text-purple-100">Columns</h4>
            <p class="text-sm text-purple-700 dark:text-purple-300">Opinion pieces, editorial content, and regular features</p>
          </div>
        </div>

        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mt-6 mb-3">Technical Architecture</h3>
        <ul class="list-disc list-inside text-gray-700 dark:text-gray-300 space-y-2">
          <li><strong>Framework:</strong> Laravel 10 with PHP 8.0+</li>
          <li><strong>Database:</strong> MySQL with dedicated CNA database connection</li>
          <li><strong>Authentication:</strong> Laravel Sanctum for API token management</li>
          <li><strong>Content Processing:</strong> Sanity Portable Text to HTML conversion</li>
          <li><strong>Media Handling:</strong> Cloudinary integration for image management</li>
          <li><strong>Search:</strong> MySQL full-text search with relevance scoring</li>
          <li><strong>Filtering:</strong> Advanced content filtering with EloquentFilter</li>
          <li><strong>Frontend:</strong> Tailwind CSS with Alpine.js for interactive components</li>
        </ul>

        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mt-6 mb-3">Integration Capabilities</h3>
        <p class="text-gray-700 dark:text-gray-300 mb-4">
          The system integrates with multiple external services including Sanity CMS for content management,
          social media platforms for content distribution, and various embed services for rich media content.
          The API supports webhook integrations for real-time content synchronization and automated publishing workflows.
        </p>

        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mt-6">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            <strong>Note:</strong> This application serves as the backend API for Catholic News Agency's digital platforms,
            providing reliable, scalable content management and distribution capabilities for faith-based journalism.
          </p>
        </div>
      </div>
    </div>
  </div>
  </x-guest-layout>
