<?php

namespace App\Services;

use App\Contracts\EmbedProxy;

class IframelyEmbedProxy implements EmbedProxy
{
    /**
     * Get the embed data for a given URL.
     *
     * @param string $url
     * @return string
     */
    public function getEmbedData(string $url): string
    {
        $apiUrl = config('iframely.api_url') . '/oembed?url=' . urlencode($url) . '&api_key=' . config('iframely.api_key');
        $response = json_decode(file_get_contents($apiUrl));
        if ($response === false) {
            throw new \Exception('Failed to fetch embed data.');
        }
        return $response->html ?? '';
    }
}
