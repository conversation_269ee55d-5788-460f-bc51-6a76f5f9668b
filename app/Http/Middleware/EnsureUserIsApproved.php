<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnsureUserIsApproved
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Auth::check() && !Auth::user()->isApproved()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Your account is pending approval. Please wait for an administrator to approve your account.'
                ], 403);
            }

            Auth::logout();
            return redirect()->route('login')->with('error', 'Your account is pending approval. Please wait for an administrator to approve your account.');
        }

        return $next($request);
    }
}
