<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckUserRole
{
    public function handle(Request $request, Closure $next, ...$roles): mixed
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();
        
        if (!$user->isApproved()) {
            Auth::logout();
            return redirect()->route('login')->with('error', 'Your account is pending approval.');
        }

        if (!in_array($user->role, $roles)) {
            abort(403, 'Insufficient permissions.');
        }

        return $next($request);
    }
}