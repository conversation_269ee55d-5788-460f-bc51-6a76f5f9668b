<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Validation\Rule;

class TokenController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'approved']);
    }

    /**
     * Display a listing of the user's tokens.
     */
    public function index(): View
    {
        $tokens = auth()->user()->tokens()->orderBy('created_at', 'desc')->get();
        
        return view('tokens.index', compact('tokens'));
    }

    /**
     * Show the form for creating a new token.
     */
    public function create(): View
    {
        return view('tokens.create');
    }

    /**
     * Store a newly created token.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'abilities' => ['array'],
            'abilities.*' => ['string', 'in:read,write,admin'],
        ]);

        // Define abilities based on user role
        $abilities = $this->getAbilitiesForUser(auth()->user(), $request->input('abilities', []));

        $token = auth()->user()->createToken(
            $request->name,
            $abilities
        );

        return redirect()->route('tokens.show', ['token' => $token->accessToken->id])
            ->with('token', $token->plainTextToken)
            ->with('success', 'Token created successfully! Make sure to copy it now as you won\'t be able to see it again.');
    }

    /**
     * Display the specified token.
     */
    public function show(Request $request, $tokenId): View
    {
        $token = auth()->user()->tokens()->findOrFail($tokenId);
        $plainTextToken = $request->session()->get('token');
        
        return view('tokens.show', compact('token', 'plainTextToken'));
    }

    /**
     * Remove the specified token.
     */
    public function destroy($tokenId): RedirectResponse
    {
        $token = auth()->user()->tokens()->findOrFail($tokenId);
        $token->delete();

        return redirect()->route('tokens.index')
            ->with('success', 'Token deleted successfully.');
    }

    /**
     * Revoke all tokens for the user.
     */
    public function destroyAll(): RedirectResponse
    {
        auth()->user()->tokens()->delete();

        return redirect()->route('tokens.index')
            ->with('success', 'All tokens have been revoked.');
    }

    /**
     * Get available abilities for a user based on their role.
     */
    private function getAbilitiesForUser($user, $requestedAbilities = []): array
    {
        $availableAbilities = [];

        // All users can read
        $availableAbilities[] = 'read';

        // Editors and admins can write
        if ($user->hasWriteAccess()) {
            $availableAbilities[] = 'write';
        }

        // Only admins can have admin abilities
        if ($user->isAdmin()) {
            $availableAbilities[] = 'admin';
        }

        // If no specific abilities requested, give all available
        if (empty($requestedAbilities)) {
            return $availableAbilities;
        }

        // Return intersection of requested and available abilities
        return array_intersect($requestedAbilities, $availableAbilities);
    }

    /**
     * Show API documentation.
     */
    public function docs(): View
    {
        return view('tokens.docs');
    }
}
