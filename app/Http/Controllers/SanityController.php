<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Tag;
use App\Models\Media;
use App\Models\Story;
use App\Models\Author;
use App\Models\Country;
use App\Models\Section;
use Sanity\BlockContent;
use App\Models\MediaType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Sanity\Factories\SerializerFactory;

class SanityController extends Controller
{
    private array $validationRules = [
        'title' => 'required',
        'publishedDate' => 'required|date',
        'slug.current' => 'required',
        'authors.*.fullName' => 'required',
        'authors.*.slug.current' => 'required',
        'tags.*.title' => 'required',
        'tags.*.slug.current' => 'required',
        'subcategory.title' => 'required',
        'subcategory.slug.current' => 'required',
    ];
    private array $article = [];

    /**
     * Create or update a story from Sanity.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function createOrUpdate(Request $request)
    {
        $validated = $request->validate([
            'data' => ['required', 'json'],
        ]);
        $this->article = json_decode($validated['data'], true);
        Validator::make($this->article, $this->validationRules)->validate();
        try {
            $story = $this->processStory();
            $message = $story->wasRecentlyCreated ? 'Story created successfully' : 'Story updated successfully';
            return response()->json([
                'message' => $message,
                'story' => $story->load(['tags', 'authors', 'country', 'section', 'media'])
            ]);
        } catch (\Exception $e) {
            // throw $e; // Re-throw the exception for global exception handling
            return response()->json([
                'message' => 'Error: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a story from Sanity.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function delete(Request $request)
    {
        $validated = $request->validate([
            'data' => ['required', 'json'],
        ]);
        $this->article = json_decode($validated['data'], true);
        Validator::make($this->article, [
            'slug.current' => 'required',
        ])->validate();
        try {
            Story::where('slug', $this->article['slug']['current'])->delete();
            return response()->json([
                'message' => 'Story deleted successfully',
            ]);
        } catch (\Exception $e) {
            // throw $e; // Re-throw the exception for global exception handling
            return response()->json([
                'message' => 'Error: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Process the article data and create or update a Story model.
     *
     * @param Story $story
     * @param [type] $this->article
     * @return Story
     */
    private function processStory()
    {
        $story = Story::updateOrCreate(
            ['slug' => $this->article['slug']['current']],
            [
                'title' => $this->article['title'],
                'excerpt' => $this->article['description'],
                'body_html' => $this->getHtmlFromPortableText($this->article['body']),
                'seo_title' => $this->article['seoMetadata']['title'] ?? $this->article['title'],
                'twitter_title' => $this->article['socialMediaMetadata']['twitterTitle'] ?? '',
                'twitter_excerpt' => $this->article['socialMediaMetadata']['twitterDescription'] ?? '',
                'twitter_tweet' => $this->article['socialMediaMetadata']['twitterTweetContent'] ?? '',
                'facebook_title' => $this->article['socialMediaMetadata']['facebookTitle'] ?? '',
                'facebook_excerpt' => $this->article['socialMediaMetadata']['facebookDescription'] ?? '',
                'is_tweeted' => $this->article['socialMediaMetadata']['isTweeted'] ?? false,
                'date_published' => Carbon::parse($this->article['publishedDate'])->setTimezone(config('app.timezone'))->format('Y-m-d H:i:s'),
                'featured_image' => $this->getFeaturedImageFromArticle(),
                'entry_type_id' => 1, // Assuming '1' is the ID for 'News'
                'is_from_sanity' => true, // Mark as Sanity imported
            ]
        );

        $country = $this->getCountryfromArticle();
        $section = $this->getSectionfromArticle();
        $story->section()->associate($section);
        $story->country()->associate($country);
        $story->save();
        $tags = $this->getTagsfromArticle();
        $authors = $this->getAuthorsfromArticle();
        $media = $this->getMediaFromArticle();

        // Associate tags, authors, and media with the story
        $story->tags()->sync($tags->pluck('id'));
        $story->authors()->sync($authors->pluck('id'));
        $story->media()->sync($media->pluck('id'));

        return $story;
    }

    /**
     * Get an array of Tag models from the article data.
     *
     * @return \Illuminate\Support\Collection
     */
    private function getTagsfromArticle()
    {
        $tags = [];
        if (isset($this->article['tags'])) {
            foreach ($this->article['tags'] as $tag) {
                $tags[] = Tag::updateOrCreate(
                    [
                        'name' => $tag['title'],
                        'slug' => $tag['slug']['current']
                    ],
                    [
                        'seo_title' => $tag['seoMetadata']['title'] ?? $tag['title'],
                        'seo_description' => $tag['seoMetadata']['description'] ?? '',
                    ]
                );
            }
        }
        return collect($tags);
    }

    /**
     * Get an array of Author models from the article data.
     *
     * @return \Illuminate\Support\Collection
     */
    private function getAuthorsfromArticle()
    {
        $authors = [];
        if (isset($this->article['authors'])) {
            foreach ($this->article['authors'] as $author) {
                $authors[] = Author::updateOrCreate(
                    [
                        'name' => $author['fullName'],
                        'slug' => $author['slug']['current']
                    ],
                    [
                        'email' => $author['email'] ?? '',
                        'excerpt' => $author['longBio'] ?? '',
                        'twitter' => collect($author['socialMedia'] ?? [])->filter(fn ($sm) => $sm['icon'] === 'twitter')->first()['link'] ?? '',
                        'gplus' => collect($author['socialMedia'] ?? [])->filter(fn ($sm) => $sm['icon'] === 'linkedin')->first()['link'] ?? '',
                        'facebook' => collect($author['socialMedia'] ?? [])->filter(fn ($sm) => $sm['icon'] === 'facebook')->first()['link'] ?? '',
                        'is_active' => 1,
                    ]
                );
            }
        }
        return collect($authors);
    }

    /**
     * Get the primary country from the article data.
     *
     * @return Country|null
     */
    private function getCountryfromArticle()
    {
        if (isset($this->article['countries']) && count($this->article['countries']) > 0) {
            // Assuming the first country is the primary one
            $country = $this->article['countries'][0] ?? null;
            if ($country) {
                return Country::firstOrCreate(
                    [
                        'name' => $country['name'],
                        'slug' => $country['slug']['current']
                    ]
                );
            }
            return null;
        }
    }

    /**
     * Get the featured image from the article data.
     *
     * @return array|null
     */
    private function getFeaturedImageFromArticle()
    {
        if (isset($this->article['featuredMedia']) && count($this->article['featuredMedia']) > 0) {
            $featuredImage = null;

            foreach ($this->article['featuredMedia'] as $media) {
                if ($media['resource_type'] === 'image') {
                    $featuredImage = [
                        'name'              => $media['context']['custom']['caption'] ?? '',
                        'data'              => $media['secure_url'],
                        'data_transformed'  => $media['secure_url'],
                        'caption'           => $media['context']['custom']['caption'] ?? '',
                        'media_type'        => [
                            'id'    => 1,
                            'name'  => 'Image',
                            'slug'  => 'image',
                        ],
                    ];
                    break;
                }
            }
            return $featuredImage;
        }
        return null;
    }

    /**
     * Get an array of Media models from the article data.
     *
     * @return \Illuminate\Support\Collection
     */
    private function getMediaFromArticle()
    {
        $media = [];
        if (isset($this->article['featuredMedia']) && count($this->article['featuredMedia']) > 0) {
            foreach ($this->article['featuredMedia'] as $item) {
                $media[] = Media::firstOrCreate(
                    [
                        'name' => $item['context']['custom']['caption'] ?? '',
                        'data' => $item['secure_url'],
                        'caption' => $item['context']['custom']['caption'] ?? '',
                        'media_type_id' => MediaType::where('slug', $item['resource_type'])->pluck('id')->first() ?? 1, // Default to 'Image' if not found
                    ]
                );
            }
        }
        return collect($media);
    }

    /**
     * Get the section from the article data.
     *
     * @return Section|null
     */
    private function getSectionfromArticle()
    {
        if (isset($this->article['subcategory']) && $this->article['subcategory']) {
            return Section::firstOrCreate(
                [
                    'title' => $this->article['subcategory']['title'],
                    'slug' => $this->article['subcategory']['slug']['current'],
                    'entry_type_id' => 1, // Assuming '1' is the ID for 'News'
                ],
            );
        }
        return null;
    }

    /**
     * Convert the Portable Text body to HTML.
     *
     * @param array $body
     * @return string
     */
    private function getHtmlFromPortableText($body)
    {
        return BlockContent::toHtml($body, [
            'useAbsoluteLinks' => true,
            'serializers' => [
                'cloudinary.asset' => fn ($block) => SerializerFactory::create('cloudinaryAsset')->serialize($block),
                'imageCarousel' => fn ($block) => SerializerFactory::create('imageCarousel')->serialize($block),
                'image' => fn ($block) => SerializerFactory::create('image')->serialize($block),
                'instagramEmbed' => fn ($block) => SerializerFactory::create('instagramEmbed')->serialize($block),
                'facebookEmbed' => fn ($block) => SerializerFactory::create('facebookEmbed')->serialize($block),
                'twitterEmbed' => fn ($block) => SerializerFactory::create('twitterEmbed')->serialize($block),
                'iframeEmbed' => fn ($block) => SerializerFactory::create('iframeEmbed')->serialize($block),
                'inlineRelatedArticles' => fn ($block) => SerializerFactory::create('dummy')->serialize($block),
                'highlightedBlockquote' => fn ($block) => SerializerFactory::create('highlightedBlockquote')->serialize($block),
                'richTextBlock' => fn ($block) => $this->getHtmlFromPortableText($block['attributes']['content']),
            ],
        ]);
    }
}
