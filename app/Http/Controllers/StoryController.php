<?php

namespace App\Http\Controllers;

use App\Models\Story;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class StoryController extends Controller
{
    public function stories(Request $request)
    {
        //Log::info(json_encode($request->all()));
        $filter = Story::with([
            'tags' => fn ($q) => $q->select('id', 'name', 'slug'),
            'city' => fn ($q) => $q->select('id', 'name', 'slug'),
            'country' => fn ($q) => $q->select('id', 'name', 'slug'),
            'section' => fn ($q) => $q->select('id', 'title', 'slug'),
            'authors' => fn ($q) => $q->select('id', 'name', 'slug'),
            'media' => fn ($q) => $q->select('id', 'name', 'entry_media.credit', 'entry_media.caption', 'data')->where('media_type_id', 1)->orderBy('entry_media.position'),
        ])
            ->select('id', 'title', 'body_html', 'facebook_title', 'seo_title', 'slug', 'date_published',
                'featured_image', 'excerpt', 'entry_type_id', 'section_id', 'city_id', 'country_id');
        if ($request->filled("title")) {
            if ($request->all()["title"] !== "") {
                //Log::info("busqueda");
                $filter = $filter->selectRaw('match(' . 'title,body_html' . ') against(? in boolean mode) as relevance_score', [$request->all()["title"]])
                    ->whereRaw('match(' . 'title,body_html' . ') against(? in boolean mode) > 0.00000001', [$request->all()["title"]]);
            }
        }
        $all = $request->all();
        if (isset($all["title"])) {
            unset($all["title"]);
        }
        $filter = $filter->where('is_available_for_editors', 1)
            ->filter($all)
        ;
        $result = $filter->queryBuilder
            ->orderByDesc('date_published')
            ->paginateFilter(15)
            ->toArray()
        ;
        $result['filter_names'] = $filter->filterNames;
        foreach ($result['data'] as &$data) {
            if (isset($data["featured_image"]->data_transformed)){
                $data['featured_image']->data_transformed = str_replace('https://admin.', 'https://www.', $data['featured_image']->data_transformed);
            }
        }

        return response()->json((object)$result);
    }

    public function story($id)
    {
        $story = Story::with([
            'tags' => fn ($q) => $q->select('id', 'name', 'slug'),
            'city' => fn ($q) => $q->select('id', 'name', 'slug'),
            'country' => fn ($q) => $q->select('id', 'name', 'slug'),
            'section' => fn ($q) => $q->select('id', 'title', 'slug'),
            'authors' => fn ($q) => $q->select('id', 'name', 'slug'),
            'media' => fn ($q) => $q->select('id', 'name', 'entry_media.credit', 'entry_media.caption', 'data')->where('media_type_id', 1)->orderBy('entry_media.position'),
        ])
            ->select('id', 'title', 'facebook_title', 'seo_title', 'subtitle', 'slug', 'date_published', 'featured_image', 'body_html', 'excerpt', 'twitter_excerpt', 'entry_type_id', 'section_id', 'city_id', 'country_id')
            ->findOrFail($id)
        ;
        $story = json_decode($story->toJson());
        $story->featured_image->data_transformed = str_replace('https://admin.', 'https://www.', $story->featured_image->data_transformed);

        // Verifica si la relación 'media' existe y no está vacía
        if (isset($story->media) && !empty($story->media)) {
            // Iterar sobre cada elemento en la relación media para adecuar a lo que recibe el API
            foreach ($story->media as $media) {
                if (isset($media->data)) {
                    $media->data_transformed = 'https://www.catholicnewsagency.com' . $media->data;
                }
            }
        }

        return $story;
    }
}
