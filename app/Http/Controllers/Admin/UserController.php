<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * Display a listing of users.
     */
    public function index(Request $request): View
    {
        $query = User::with('approver');

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Apply role filter
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        // Apply status filter
        if ($request->filled('status')) {
            if ($request->status === 'approved') {
                $query->where('is_approved', true);
            } elseif ($request->status === 'pending') {
                $query->where('is_approved', false);
            }
        }

        // Apply sorting
        $sortBy = $request->input('sort', 'created_at');
        $sortOrder = $request->input('order', 'desc');

        if (in_array($sortBy, ['name', 'email', 'role', 'created_at', 'approved_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $users = $query->paginate(15)->appends($request->query());

        return view('admin.users.index', compact('users'));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create(): View
    {
        return view('admin.users.create');
    }

    /**
     * Store a newly created user.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'role' => ['required', 'in:admin,editor,user'],
            'is_approved' => ['boolean'],
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => bcrypt($request->password),
            'role' => $request->role,
            'is_approved' => $request->boolean('is_approved'),
            'approved_at' => $request->boolean('is_approved') ? now() : null,
            'approved_by' => $request->boolean('is_approved') ? auth()->id() : null,
        ]);

        return redirect()->route('admin.users.index')
            ->with('success', 'User created successfully.');
    }

    /**
     * Show the form for editing a user.
     */
    public function edit(User $user): View
    {
        return view('admin.users.edit', compact('user'));
    }

    /**
     * Update the specified user.
     */
    public function update(Request $request, User $user): RedirectResponse
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'role' => ['required', 'in:admin,editor,user'],
            'password' => ['nullable', 'string', 'min:8', 'confirmed'],
        ]);

        $data = [
            'name' => $request->name,
            'email' => $request->email,
            'role' => $request->role,
        ];

        if ($request->filled('password')) {
            $data['password'] = bcrypt($request->password);
        }

        $user->update($data);

        return redirect()->route('admin.users.index')
            ->with('success', 'User updated successfully.');
    }

    /**
     * Approve a user.
     */
    public function approve(Request $request, User $user): RedirectResponse
    {
        $request->validate([
            'approval_notes' => ['nullable', 'string', 'max:1000'],
        ]);

        $user->approve(auth()->user(), $request->approval_notes);

        return redirect()->back()
            ->with('success', 'User approved successfully.');
    }

    /**
     * Reject/unapprove a user.
     */
    public function reject(Request $request, User $user): RedirectResponse
    {
        $request->validate([
            'approval_notes' => ['nullable', 'string', 'max:1000'],
        ]);

        $user->reject($request->approval_notes);

        return redirect()->back()
            ->with('success', 'User approval revoked.');
    }

    /**
     * Remove the specified user.
     */
    public function destroy(User $user): RedirectResponse
    {
        if ($user->id === auth()->id()) {
            return redirect()->back()
                ->with('error', 'You cannot delete your own account.');
        }

        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }

    /**
     * Display pending users for approval.
     */
    public function pending(): View
    {
        $pendingUsers = User::where('is_approved', false)
            ->orderBy('created_at', 'asc')
            ->paginate(15);

        return view('admin.users.pending', compact('pendingUsers'));
    }

    /**
     * Bulk approve users.
     */
    public function bulkApprove(Request $request): RedirectResponse
    {
        $request->validate([
            'selected_users' => ['required', 'array'],
            'selected_users.*' => ['exists:users,id'],
        ]);

        $users = User::whereIn('id', $request->selected_users)
            ->where('is_approved', false)
            ->get();

        foreach ($users as $user) {
            $user->approve(auth()->user(), 'Bulk approved by admin');
        }

        return redirect()->back()
            ->with('success', count($users) . ' users approved successfully.');
    }

    /**
     * Bulk delete users.
     */
    public function bulkDelete(Request $request): RedirectResponse
    {
        $request->validate([
            'selected_users' => ['required', 'array'],
            'selected_users.*' => ['exists:users,id'],
        ]);

        $users = User::whereIn('id', $request->selected_users)
            ->where('id', '!=', auth()->id()) // Prevent self-deletion
            ->get();

        // Check if trying to delete the last admin
        $adminUsers = $users->where('role', 'admin');
        if ($adminUsers->count() > 0) {
            $totalAdmins = User::where('role', 'admin')->count();
            if ($totalAdmins - $adminUsers->count() < 1) {
                return redirect()->back()
                    ->with('error', 'Cannot delete all admin users. At least one admin must remain.');
            }
        }

        $deletedCount = $users->count();
        User::whereIn('id', $users->pluck('id'))->delete();

        return redirect()->back()
            ->with('success', $deletedCount . ' users deleted successfully.');
    }
}