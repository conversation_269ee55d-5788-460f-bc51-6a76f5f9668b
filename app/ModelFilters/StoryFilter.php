<?php

namespace App\ModelFilters;

use App\Models\Tag;
use App\Models\City;
use App\Models\Author;
use App\Models\Country;
use App\Models\Section;
use EloquentFilter\ModelFilter;

class StoryFilter extends ModelFilter
{
    // use FullTextable;

    /**
     * Related Models that have ModelFilters as well as the method on the ModelFilter
     * As [relationMethod => [input_key1, input_key2]].
     *
     * @var array
     */
    public $relations = [];
    public $filterNames = [];
    public $queryBuilder;

    // public function dateFrom($date_from)
    // {
    //     return $this->whereDate('entries.date_published', '>=', $date_from);
    // }

    // public function dateTo($date_to)
    // {
    //     return $this->whereDate('entries.date_published', '<=', $date_to);
    // }

    // public function date($date)
    // {
    //     return $this->whereDate('entries.date_published', $date);
    // }

    public function tag($tag)
    {
        $tag = Tag::find((int)$tag);
        $this->filterNames[] = is_null($tag) ? null : 'Tag: '.$tag->name;

        return $this->whereHas('tags', function ($q) use ($tag) {
            $q->where('tag_id', $tag->id);
        });
    }

    public function author($author)
    {
        $author = Author::find((int)$author);
        $this->filterNames[] = is_null($author) ? null : 'Author: '.$author->name;

        return $this->whereHas('authors', function ($q) use ($author) {
            $q->where('author_id', $author->id);
        });
    }

    public function section($section)
    {
        $section = Section::find((int)$section);
        $this->filterNames[] = is_null($section) ? null : 'Section: '.$section->title;

        return $this->where('section_id', $section->id);
    }

    public function country($country)
    {
        $country = Country::find((int)$country);
        $this->filterNames[] = is_null($country) ? null : 'Country: '.$country->name;

        return $this->where('country_id', $country->id);
    }

    public function city($city)
    {
        $city = City::find((int)$city);
        $this->filterNames[] = is_null($city) ? null : 'City: '.$city->name;

        return $this->where('city_id', $city->id);
    }

    // public function isDraft($is_draft)
    // {
    //     return $this->where('entries.is_draft', $is_draft);
    // }

    // public function isMainHeadline($is_main_headline)
    // {
    //     return $this->where('entries.is_main_headline', $is_main_headline);
    // }
}
