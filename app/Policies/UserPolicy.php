<?php

namespace App\Policies;

use App\Models\User;

class UserPolicy
{
    /**
     * Determine if the user can view any users.
     */
    public function viewAny(User $user): bool
    {
        return $user->canManageUsers();
    }

    /**
     * Determine if the user can create users.
     */
    public function create(User $user): bool
    {
        return $user->canManageUsers();
    }

    /**
     * Determine if the user can update users.
     */
    public function update(User $user, User $model): bool
    {
        return $user->canManageUsers() || $user->id === $model->id;
    }

    /**
     * Determine if the user can delete users.
     */
    public function delete(User $user, User $model): bool
    {
        return $user->canManageUsers() && $user->id !== $model->id;
    }

    /**
     * Determine if the user can approve other users.
     */
    public function approve(User $user): bool
    {
        return $user->canManageUsers();
    }
}