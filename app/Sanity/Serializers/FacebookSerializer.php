<?php

namespace App\Sanity\Serializers;

use App\Sanity\Contracts\Serializer;

class FacebookSerializer implements Serializer
{
    public function serialize($asset): string
    {
        $block = $asset['attributes'];
        $url = $block['url'] ?? '';
        // Return the Facebook post HTML
        return <<<HTML
            <script async defer src="https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v3.2"></script>  
            <div style="margin: 20px 0; text-align: center;">
                <div class="fb-post" 
                    data-href="{$url}"
                    data-width="500">Loading Facebook post...</div>
            </div>
        HTML;
    }

}
