<?php

namespace App\Sanity\Serializers;

use App\Facades\EmbedProxy;
use App\Sanity\Contracts\Serializer;
use App\Sanity\ExceptionHandlers\BlockquoteExceptionHandler;

class EmbedSerializer implements Serializer
{
    /**
     * Serialize the given data.
     *
     * @param mixed $asset
     * @return string
     */
    public function serialize($asset): string
    {
        try {
            $block = $asset['attributes'];
            $url = $block['url'] ?? null;
            return '<div style="text-align: center">' . EmbedProxy::getEmbedData($url) . '</div>';
        } catch (\Exception $e) {
            // Handle the exception as needed, e.g., log it or return a default value
            return BlockquoteExceptionHandler::handle($e, 'There was an error serializing the image'); // Log the error or handle it as needed
        }
    }
}
