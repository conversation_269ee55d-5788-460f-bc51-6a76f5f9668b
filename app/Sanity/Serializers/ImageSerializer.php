<?php

namespace App\Sanity\Serializers;

use App\Sanity\Contracts\Serializer;
use App\Sanity\ExceptionHandlers\BlockquoteExceptionHandler;

class ImageSerializer implements Serializer
{
    /**
     * Serialize an image asset.
     *
     * @param array $asset
     * @return string
     */
    public function serialize($asset): string
    {
        try {
            $block = $asset['attributes'] ?? $asset;
            $image = '<figure class="mx-auto" style="width:100%"><img src="' . $block['secure_url'] . '" class="img-fluid"';
            $caption = '';
            if (isset($block['context']['custom']['caption'])) {
                $image .= ' alt="' . htmlspecialchars($block['context']['custom']['caption']) . '"';
                $caption = '<figcaption class="caption text-muted">' . $block['context']['custom']['caption'] . '</figcaption>';
            }
            $image .= '>' . $caption . '</figure>';
            return $image;
        } catch (\Exception $e) {
            return BlockquoteExceptionHandler::handle($e, 'There was an error serializing the image'); // Log the error or handle it as needed
        }
    }
}
