<?php

namespace App\Sanity\Serializers;

use App\Sanity\Contracts\Serializer;
use App\Sanity\Factories\SerializerFactory;
use App\Sanity\ExceptionHandlers\BlockquoteExceptionHandler;

class CloudinaryAssetSerializer implements Serializer
{
    /**
     * Serialize a Cloudinary asset.
     *
     * @param array $asset
     * @return string
     */
    public function serialize($asset): string
    {
        try {
            return SerializerFactory::create($asset['attributes']['resource_type'])->serialize($asset);
        } catch (\Exception $e) {
            return BlockquoteExceptionHandler::handle($e, 'Serialization error'); // Log the error or handle it as needed
        }
    }
}
