<?php

namespace App\Sanity\Serializers;

use App\Sanity\Contracts\Serializer;

class TwitterDecorator implements Serializer
{
    
    private $serializer;

    public function __construct(Serializer $serializer)
    {
        $this->serializer = $serializer;
    }
    
    /**
     * Serialize a Twitter embed block. This is a decorator for
     * the EmbedSerializer to ensure it is wrapped in a div
     *
     * @param array $asset
     * @return string
     */
    public function serialize($asset): string
    {
        return <<<HTML
            <div style="width: 100%; margin: 20px auto; max-width: 550px;">
                {$this->serializer->serialize($asset)}
            </div>
        HTML;
    }
} 