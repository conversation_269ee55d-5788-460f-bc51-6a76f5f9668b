<?php

namespace App\Sanity\Serializers;

use App\Sanity\Contracts\Serializer;
use App\Sanity\Factories\SerializerFactory;

class CarouselSerializer implements Serializer
{
    /**
     * Serialize a carousel block.
     *
     * @param array $block
     * @return string
     */
    public function serialize($asset): string
    {
        $items = [];
        $block = $asset['attributes'];
        foreach ($block['images'] as $item) {
            $items[] = SerializerFactory::create($item['resource_type'])->serialize($item);
        }
        $items = collect($items);

        return <<<HTML
            <div class="post-slider">
                <div class="swiper-container">
                    <div class="swiper-wrapper">
                        {$items->map(fn($item) => "<div class=\"swiper-slide\">{$item}</div>")->implode('')}
                    </div>
                    <div class="cna-arrows">
                        <div class="swiper-button-next swiper-button-white"></div>
                        <div class="swiper-button-prev swiper-button-white"></div>
                    </div>
                    <div class="swiper-pagination"></div>
                </div>
            </div>
        HTML;
    }
}
