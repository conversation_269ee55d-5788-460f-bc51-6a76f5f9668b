<?php

namespace App\Sanity\Factories;

use App\Sanity\Contracts\Serializer;
use App\Sanity\Serializers\DummySerializer;
use App\Sanity\Serializers\EmbedSerializer;
use App\Sanity\Serializers\ImageSerializer;
use App\Sanity\Serializers\TwitterDecorator;
use App\Sanity\Serializers\CarouselSerializer;
use App\Sanity\Serializers\FacebookSerializer;
use App\Sanity\Serializers\InstagramSerializer;
use App\Sanity\Serializers\CloudinaryAssetSerializer;
use App\Sanity\Serializers\HighlightedBlockquoteSerializer;

class SerializerFactory
{
    /**
     * Create a serializer instance based on the type.
     *
     * @param string $type
     * @return Serializer
     */
    public static function create(string $type): Serializer
    {
        switch ($type) {
            case 'cloudinaryAsset': return new CloudinaryAssetSerializer();
            case 'imageCarousel': return new CarouselSerializer();
            case 'image': return new ImageSerializer();
            case 'instagramEmbed': return new InstagramSerializer();
            case 'facebookEmbed': return new FacebookSerializer();
            case 'twitterEmbed': return new TwitterDecorator(new EmbedSerializer());
            case 'iframeEmbed': return new EmbedSerializer();
            case 'highlightedBlockquote': return new HighlightedBlockquoteSerializer();
            case 'dummy': return new DummySerializer();
        }

        // Not found? Throw an exception. This may be a custom serializer that needs to be implemented.
        // Also, this might go within the switch statement as a default case.
        throw new \InvalidArgumentException("Serializer class for type '{$type}' does not exist.");
    }
}
