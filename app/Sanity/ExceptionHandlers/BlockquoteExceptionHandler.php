<?php

namespace App\Sanity\ExceptionHandlers;

use Carbon\Exceptions\Exception;
use App\Sanity\Contracts\ExceptionHandler;

class BlockquoteExceptionHandler implements ExceptionHandler
{
    /**
     * Handle exceptions during serialization.
     *
     * @param \Exception $e
     * @return string
     */
    public static function handle(\Exception $e, string $message = "Error"): string
    {
        // Log the error or handle it as needed
        // For example, you can use a logger service here
        return '<blockquote style="margin-block-start: 1em; margin-block-end: 1em; margin-inline-start: 40px; margin-inline-end: 40px; color: crimson; line-height: 1; font-size: 0.9em; font-style: italic;"><h4>'. $message . '</h4><p>' . htmlspecialchars($e->getMessage()) . '</p></blockquote>';
    }
}
