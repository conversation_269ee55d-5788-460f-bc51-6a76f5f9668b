<?php

namespace App\Providers;

use App\Contracts\EmbedProxy;
use App\Services\IframelyEmbedProxy;
use Illuminate\Support\ServiceProvider;

class EmbedProxyServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->bind(EmbedProxy::class, IframelyEmbedProxy::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
