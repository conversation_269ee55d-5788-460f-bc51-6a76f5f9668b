<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Leewillis77\CachedEmbed\CachedEmbed;

class Media extends Model
{
    protected $appends = ['data_transformed'];
    protected $fillable = ['name', 'caption', 'credit', 'data', 'media_type_id'];
    protected $table = 'media';
    protected $connection = 'mysql_cna';
    
    public function articles()
    {
        return $this->belongsToMany(Article::class, 'entry_media', 'media_id', 'entry_id');
    }

    public function getDataTransformedAttribute()
    {
        switch ($this->media_type_id) {
            case 1:
            case 2:
            case 3:
                return filter_var($this->data, FILTER_VALIDATE_URL) ? str_replace('https://admin.', 'https://www.', $this->data) : asset($this->data);
            case 4:
                return $this->data;
            case 5:
                $info = CachedEmbed::create($this->data);
                $data = $info->code;
                if (is_int($info->width) && is_int($info->height)) {
                    $ratio = 100 * $info->height / $info->width;
                    $data = '<div class="iframe-container" style="padding-bottom: ' . $ratio . '%;">' . $info->code . '</div>';
                }

                return $data;
        }

        return;
    }

    public function mediaType()
    {
        return $this->belongsTo(MediaType::class);
    }

    public function resources()
    {
        return $this->belongsToMany(Resource::class, 'entry_media', 'media_id', 'entry_id');
    }

    public function stories()
    {
        return $this->belongsToMany(Story::class, 'entry_media', 'media_id', 'entry_id');
    }
}
