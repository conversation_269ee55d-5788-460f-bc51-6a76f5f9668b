<?php

namespace App\Models;

// use Kalnoy\Nestedset\NodeTrait;
use Illuminate\Database\Eloquent\Model;

class Section extends Model
{
    // use NodeTrait;
    protected $connection= 'mysql_cna';

    protected $guarded = [];

    protected $casts = [
        'body_array' => 'array',
        'is_active' => 'boolean',
        'featured_image' => 'object',
    ];

    public function taxonomy()
    {
        return $this->belongsTo(Taxonomy::class, 'entry_type_id');
    }

    public function stories()
    {
        return $this->hasMany(Story::class);
    }

    public function resources()
    {
        return $this->hasMany(Resource::class);
    }

    public function columns()
    {
        return $this->hasMany(Column::class);
    }

    public function scopeForStories()
    {
        return $this->where('entry_type_id', 1);
    }

    public function scopeForResources()
    {
        return $this->where('entry_type_id', 2);
    }

    public function scopeForColumns()
    {
        return $this->where('entry_type_id', 3);
    }

    public function setIsActiveAttribute($value)
    {
        $this->attributes['is_active'] = filter_var($value, FILTER_VALIDATE_BOOLEAN);
    }
}
