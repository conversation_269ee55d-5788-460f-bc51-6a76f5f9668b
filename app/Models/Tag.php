<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Tag extends Model
{
    protected $connection= 'mysql_cna';

    protected $guarded = [];
    protected $casts = [
        'featured_image' => 'object',
    ];

    public function stories()
    {
        return $this->belongsToMany(Story::class, 'entry_tag', 'tag_id', 'entry_id');
    }

    public function articles()
    {
        return $this->belongsToMany(Article::class, 'entry_tag', 'tag_id', 'entry_id');
    }

    public function resources()
    {
        return $this->belongsToMany(Resource::class, 'entry_tag', 'tag_id', 'entry_id');
    }

    public function taggables()
    {
        return $this->belongsToMany(Entry::class, 'entry_tag', 'tag_id', 'entry_id');
    }

    // protected static function boot()
    // {
    //     parent::boot();
    //     static::addGlobalScope('unmerged', function (Builder $builder) {
    //         $builder->whereNull('merged_tag_id');
    //     });
    // }
}
