<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Author extends Model
{
    protected $connection= 'mysql_cna';

    protected $guarded = [];

    protected $appends = ['photo_url'];

    public function entries()
    {
        return $this->belongsToMany(Entry::class, 'author_entry', 'author_id', 'entry_id');
    }

    public function articles()
    {
        return $this->belongsToMany(Article::class, 'author_entry', 'author_id', 'entry_id');
    }

    public function resources()
    {
        return $this->belongsToMany(Resource::class, 'author_entry', 'author_id', 'entry_id');
    }

    public function stories()
    {
        return $this->belongsToMany(Story::class, 'author_entry', 'author_id', 'entry_id');
    }

    public function taxonomies()
    {
        return $this->belongsToMany(Taxonomy::class, 'author_entry_type', 'author_id', 'entry_type_id');
    }

    public function getPhotoUrlAttribute()
    {
        if ($this->photo) {
            return filter_var($this->photo, FILTER_VALIDATE_URL) ? $this->photo : asset(Storage::url($this->photo));
        }

        return null;
    }
}
