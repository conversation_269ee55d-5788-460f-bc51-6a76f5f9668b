<?php

namespace App\Models;

use EloquentFilter\Filterable;
use Illuminate\Database\Eloquent\Builder;

class Story extends Entry
{
    use Filterable;
    protected $table = 'entries';

    /**
     * Creates local scope to run the filter.
     *
     * @param $query
     * @param null|ModelFilter|string $filter
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFilter($query, array $input = [], $filter = null)
    {
        // Resolve the current Model's filter
        if ($filter === null) {
            $filter = $this->getModelFilterClass();
        }

        // Create the model filter instance
        $modelFilter = new $filter($query, $input);

        // Set the input that was used in the filter (this will exclude empty strings)
        $this->filtered = $modelFilter->input();

        // Return the filter query
        $modelFilter->queryBuilder = $modelFilter->handle();

        return $modelFilter;
    }

    protected static function boot()
    {
        parent::boot();
        static::addGlobalScope('entry_type_id', function (Builder $builder) {
            $builder->where('entry_type_id', '=', 1);
        });
    }
}
