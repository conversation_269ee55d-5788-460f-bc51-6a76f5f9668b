<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Entry extends Model
{
    protected $connection = 'mysql_cna';

    protected $guarded = [];
    protected $casts = [
        'featured_image' => 'object',
        'twitter_image' => 'object',
        'facebook_image' => 'object',
        'body_array' => 'json',
        'is_draft' => 'boolean',
        'is_last_minute' => 'boolean',
        'is_main_headline' => 'boolean',
        'is_hidden_featured_image' => 'boolean',
        'is_available_for_editors' => 'boolean',
        'is_available_for_times' => 'boolean',
        'is_from_sanity' => 'boolean',
        'is_tweeted' => 'boolean',
        'date_published' => 'datetime',
        'updated_at' => 'datetime',
        'created_at' => 'datetime',
    ];
    protected $appends = ['url', 'canonical_url'];
    protected $dates = ['date_published'];
    protected $types = [
        1 => 'news',
        2 => 'resource',
        3 => 'column',
    ];

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function city()
    {
        return $this->belongsTo(City::class);
    }

    public function agency()
    {
        return $this->belongsTo(Agency::class);
    }

    public function section()
    {
        return $this->belongsTo(Section::class);
    }

    public function taxonomy()
    {
        return $this->belongsTo(Taxonomy::class);
    }

    public function entryType()
    {
        return $this->belongsTo(EntryType::class);
    }

    public function tags()
    {
        return $this->belongsToMany(Tag::class, 'entry_tag', 'entry_id', 'tag_id');
    }

    public function authors()
    {
        return $this->belongsToMany(Author::class, 'author_entry', 'entry_id', 'author_id')
            ->withPivot('position')->orderBy('author_entry.position', 'asc');
    }

    public function media()
    {
        return $this->belongsToMany(Media::class, 'entry_media', 'entry_id', 'media_id')
            ->withPivot('credit', 'caption', 'position')->as('details')->orderBy('entry_media.position', 'asc');
    }

    public function related()
    {
        return $this->belongsToMany(Entry::class, 'entries_related', 'id', 'related_id')
            ->withPivot('position')->orderBy('entries_related.position', 'asc');
    }

    // Relación temporal, solo para reparar tabla. La clave 'media_id' se eliminará.

    public function featuredImageData()
    {
        return $this->belongsTo(Media::class, 'media_id');
    }

    public function getUrlAttribute()
    {
        return config('app.frontend_url')."/{$this->types[$this->entry_type_id]}/{$this->id}/{$this->slug}";
    }

    public function getCanonicalUrlAttribute()
    {
        return config('app.frontend_url')."/{$this->types[$this->entry_type_id]}/{$this->id}/{$this->slug}";
    }

    public function getTitleAttribute($value)
    {
        return $value ?: null;
    }

    public function getSeoTitleAttribute($value)
    {
        return $value ?: null;
    }

    public function getTwitterTitleAttribute($value)
    {
        return $value ?: null;
    }

    public function getFacebookTitleAttribute($value)
    {
        return $value ?: null;
    }

    protected static function boot()
    {
        parent::boot();
        static::addGlobalScope('isNotDraft', function (Builder $builder) {
            $builder->where('is_draft', 0);
        });
        static::addGlobalScope('isPublished', function (Builder $builder) {
            $builder->where('date_published', '<=', Carbon::now()->toDateTimeString());
        });
    }
}
