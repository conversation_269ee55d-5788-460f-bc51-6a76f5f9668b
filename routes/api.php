<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\StoryController;
use App\Http\Controllers\SanityController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Route::middleware('auth:api')->get('stories',[StoryController::class,'stories']);
// Route::middleware('auth:api')->get('stories/{id}',[StoryController::class,'story']);

Route::middleware(['auth:sanctum'])->get('/user', function (Request $request) {
    return $request->user();
});

Route::get('stories', [StoryController::class, 'stories']);
Route::get('stories/{id}', [StoryController::class, 'story']);
Route::post('sanity/article/upsert', [SanityController::class, 'createOrUpdate']);
Route::post('sanity/article/delete', [SanityController::class, 'delete']);
Route::post('/tokens/create', function (Request $request) {
    $token = $request->user()->createToken($request->token_name);

    return ['token' => $token->plainTextToken];
});
