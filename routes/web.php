<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // API Token Management
    Route::get('/tokens', [App\Http\Controllers\TokenController::class, 'index'])->name('tokens.index');
    Route::get('/tokens/create', [App\Http\Controllers\TokenController::class, 'create'])->name('tokens.create');
    Route::post('/tokens', [App\Http\Controllers\TokenController::class, 'store'])->name('tokens.store');
    Route::get('/tokens/{token}', [App\Http\Controllers\TokenController::class, 'show'])->name('tokens.show');
    Route::delete('/tokens/{token}', [App\Http\Controllers\TokenController::class, 'destroy'])->name('tokens.destroy');
    Route::delete('/tokens', [App\Http\Controllers\TokenController::class, 'destroyAll'])->name('tokens.destroy-all');

    // API Documentation
    Route::get('/api/docs', [App\Http\Controllers\TokenController::class, 'docs'])->name('api.docs');
});

// Admin routes
Route::middleware(['auth', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::resource('users', App\Http\Controllers\Admin\UserController::class);
    Route::get('users/pending/list', [App\Http\Controllers\Admin\UserController::class, 'pending'])->name('users.pending');
    Route::post('users/{user}/approve', [App\Http\Controllers\Admin\UserController::class, 'approve'])->name('users.approve');
    Route::post('users/{user}/reject', [App\Http\Controllers\Admin\UserController::class, 'reject'])->name('users.reject');
    Route::post('users/bulk/approve', [App\Http\Controllers\Admin\UserController::class, 'bulkApprove'])->name('users.bulk-approve');
    Route::post('users/bulk/delete', [App\Http\Controllers\Admin\UserController::class, 'bulkDelete'])->name('users.bulk-delete');
});

require __DIR__.'/auth.php';
