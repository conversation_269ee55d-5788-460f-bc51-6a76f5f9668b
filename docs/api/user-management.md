# User Management API Documentation

## Overview

The User Management API provides comprehensive endpoints for user authentication, authorization, and administration. All API responses follow a consistent JSON format with proper HTTP status codes.

## Base URL

```
https://your-domain.com/api/v1
```

## Authentication

The API uses Laravel Sanctum for authentication. Include the Bearer token in the Authorization header:

```
Authorization: Bearer your-token-here
```

## Response Format

All API responses follow this structure:

```json
{
  "status": "success|error",
  "message": "Human readable message",
  "data": {}, // Present on success
  "errors": {}, // Present on validation errors
  "error": "error_code", // Present on errors
  "meta": {
    "timestamp": "2024-01-01T00:00:00.000000Z",
    "version": "1.0"
  }
}
```

## Authentication Endpoints

### Register User

**POST** `/auth/register`

Register a new user account. New users require admin approval before they can access protected resources.

**Request Body:**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123",
  "device_name": "Mobile App" // optional
}
```

**Response (201):**
```json
{
  "status": "success",
  "message": "User registered successfully. Your account is pending approval.",
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "user",
      "is_approved": false,
      "created_at": "2024-01-01T00:00:00.000000Z"
    },
    "token": "1|abc123...",
    "token_type": "Bearer"
  }
}
```

### Login

**POST** `/auth/login`

Authenticate user and receive access token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "device_name": "Mobile App" // optional
}
```

**Response (200):**
```json
{
  "status": "success",
  "message": "Login successful",
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "user",
      "is_approved": true
    },
    "token": "1|abc123...",
    "token_type": "Bearer"
  }
}
```

### Logout

**POST** `/auth/logout`

Revoke current access token.

**Headers:** `Authorization: Bearer token`

**Response (200):**
```json
{
  "status": "success",
  "message": "Logout successful"
}
```

### Get Current User

**GET** `/auth/user`

Get authenticated user information.

**Headers:** `Authorization: Bearer token`

**Response (200):**
```json
{
  "status": "success",
  "message": "User retrieved successfully",
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "role": "user",
    "is_approved": true,
    "email_verified_at": "2024-01-01T00:00:00.000000Z",
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}
```

## User Management Endpoints

### List Users (Admin Only)

**GET** `/users`

Get paginated list of users with filtering and sorting options.

**Headers:** `Authorization: Bearer admin-token`

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `per_page` (int): Items per page (default: 15, max: 100)
- `role` (string): Filter by role (admin, editor, user)
- `is_approved` (boolean): Filter by approval status
- `search` (string): Search in name and email
- `sort_by` (string): Sort field (name, email, role, created_at, approved_at)
- `sort_order` (string): Sort direction (asc, desc)

**Example:** `/users?role=user&is_approved=false&sort_by=created_at&sort_order=asc`

**Response (200):**
```json
{
  "status": "success",
  "message": "Users retrieved successfully",
  "data": [
    {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "user",
      "is_approved": false,
      "created_at": "2024-01-01T00:00:00.000000Z"
    }
  ],
  "meta": {
    "total": 50,
    "count": 15,
    "per_page": 15,
    "current_page": 1,
    "total_pages": 4,
    "has_more_pages": true
  },
  "links": {
    "first": "http://api.example.com/users?page=1",
    "last": "http://api.example.com/users?page=4",
    "prev": null,
    "next": "http://api.example.com/users?page=2",
    "self": "http://api.example.com/users?page=1"
  }
}
```

### Create User (Admin Only)

**POST** `/users`

Create a new user account.

**Headers:** `Authorization: Bearer admin-token`

**Request Body:**
```json
{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123",
  "role": "editor",
  "is_approved": true,
  "approval_notes": "Pre-approved by admin"
}
```

**Response (201):**
```json
{
  "status": "success",
  "message": "User created successfully",
  "data": {
    "id": 2,
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "role": "editor",
    "is_approved": true,
    "approved_at": "2024-01-01T00:00:00.000000Z",
    "created_at": "2024-01-01T00:00:00.000000Z"
  }
}
```

### Get User

**GET** `/users/{id}`

Get specific user details. Users can view their own profile, admins can view any user.

**Headers:** `Authorization: Bearer token`

**Response (200):**
```json
{
  "status": "success",
  "message": "User retrieved successfully",
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "role": "user",
    "is_approved": true,
    "approval_notes": "Approved by admin",
    "approver": {
      "id": 2,
      "name": "Admin User",
      "email": "<EMAIL>"
    },
    "created_at": "2024-01-01T00:00:00.000000Z"
  }
}
```

### Update User

**PATCH** `/users/{id}`

Update user information. Users can update their own profile, admins can update any user.

**Headers:** `Authorization: Bearer token`

**Request Body:**
```json
{
  "name": "John Updated",
  "email": "<EMAIL>",
  "password": "newpassword123", // optional
  "password_confirmation": "newpassword123", // required if password provided
  "role": "editor" // admin only
}
```

### Approve User (Admin Only)

**POST** `/users/{id}/approve`

Approve a pending user account.

**Headers:** `Authorization: Bearer admin-token`

**Request Body:**
```json
{
  "approval_notes": "User verified and approved"
}
```

### Reject User (Admin Only)

**POST** `/users/{id}/reject`

Revoke user approval.

**Headers:** `Authorization: Bearer admin-token`

**Request Body:**
```json
{
  "approval_notes": "Account suspended for policy violation"
}
```

### Delete User (Admin Only)

**DELETE** `/users/{id}`

Delete a user account. Cannot delete own account or the last admin.

**Headers:** `Authorization: Bearer admin-token`

**Response (200):**
```json
{
  "status": "success",
  "message": "User deleted successfully"
}
```

## Error Responses

### Validation Error (422)
```json
{
  "status": "error",
  "message": "The given data was invalid",
  "error": "validation_failed",
  "errors": {
    "email": ["The email has already been taken."],
    "password": ["The password confirmation does not match."]
  }
}
```

### Unauthorized (401)
```json
{
  "status": "error",
  "message": "Unauthenticated",
  "error": "unauthorized"
}
```

### Forbidden (403)
```json
{
  "status": "error",
  "message": "Your account is pending approval",
  "error": "account_pending_approval",
  "data": {
    "is_approved": false,
    "approval_status": "pending"
  }
}
```

### Not Found (404)
```json
{
  "status": "error",
  "message": "Resource not found",
  "error": "not_found"
}
```

## Rate Limiting

API requests are limited to 60 requests per minute per IP address. Rate limit headers are included in responses:

```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: **********
```

## Testing

Use tools like Postman, Insomnia, or curl to test the API endpoints. Example curl commands:

```bash
# Register
curl -X POST http://localhost:8000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"name":"Test User","email":"<EMAIL>","password":"password123","password_confirmation":"password123"}'

# Login
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Get users (with token)
curl -X GET http://localhost:8000/api/v1/users \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```
