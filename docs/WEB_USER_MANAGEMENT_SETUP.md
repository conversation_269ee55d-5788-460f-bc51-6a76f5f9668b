# Web-Based User Management System Setup Guide

## Overview

This guide covers the complete web-based user management system that allows users to register, manage their profiles, create API tokens, and access your existing API endpoints. Administrators can manage users, approve accounts, and monitor system usage through a comprehensive web interface.

## Features Implemented

### ✅ **User Dashboard**
- Enhanced dashboard with account status overview
- Quick access to profile, API tokens, and admin functions
- Account approval status indicators
- API access information and statistics

### ✅ **API Token Management**
- Create, view, and revoke API tokens through web interface
- Token abilities based on user roles (read, write, admin)
- Usage examples and documentation
- Security best practices and warnings

### ✅ **Admin User Management**
- Enhanced user listing with search and filtering
- User statistics dashboard
- Bulk operations (approve/delete multiple users)
- Dedicated pending approvals interface
- Role-based access control

### ✅ **User Profile Management**
- Enhanced profile editing with account status
- API access overview
- Role and permission display
- Account history and approval information

### ✅ **User Approval Workflow**
- Dedicated pending users interface
- Bulk approval/rejection capabilities
- Approval notes and tracking
- Email notifications (ready for implementation)

## Installation and Setup

### 1. Database Setup

Ensure your database is up to date with all migrations:

```bash
php artisan migrate
```

### 2. Create Initial Admin User

Create an admin user to manage the system:

```bash
php artisan tinker
```

```php
$admin = \App\Models\User::create([
    'name' => 'System Administrator',
    'email' => '<EMAIL>',
    'password' => bcrypt('secure-password-here'),
    'role' => 'admin',
    'is_approved' => true,
    'approved_at' => now(),
]);
```

### 3. Configure Middleware

The system uses the existing middleware. Ensure these are properly registered in `app/Http/Kernel.php`:

- `approved` - Ensures user is approved
- `role` - Checks user roles
- `api.approved` - API-specific approval check
- `api.role` - API-specific role check

### 4. Test the System

1. **Register a new user** at `/register`
2. **Login as admin** and approve the user
3. **Test API token creation** and usage
4. **Verify role-based access** works correctly

## User Workflow

### For Regular Users

1. **Registration**: Users register at `/register`
   - Account created with `is_approved = false`
   - Limited access until approved

2. **Dashboard Access**: After login, users see:
   - Account status (pending/approved)
   - Profile management links
   - API token management (if approved)

3. **API Token Management**: Approved users can:
   - Create tokens with appropriate abilities
   - View active tokens and usage
   - Revoke tokens when needed
   - Access API documentation

4. **Profile Management**: Users can:
   - Update name and email
   - Change password
   - View account information
   - See API access status

### For Administrators

1. **User Management**: Access via `/admin/users`
   - View all users with filtering/search
   - See user statistics
   - Manage user roles and status

2. **Pending Approvals**: Access via navigation menu
   - Dedicated interface for pending users
   - Bulk approval/rejection
   - Individual user review

3. **System Monitoring**: Dashboard shows:
   - Total users and statistics
   - Recent registrations
   - Approval metrics

## Key Pages and Routes

### User Pages
- `/dashboard` - Enhanced user dashboard
- `/profile` - Enhanced profile management
- `/tokens` - API token management
- `/tokens/create` - Create new API token
- `/tokens/{id}` - View specific token
- `/api/docs` - API documentation

### Admin Pages
- `/admin/users` - User management with filtering
- `/admin/users/pending/list` - Pending approvals
- `/admin/users/create` - Create new user
- `/admin/users/{id}/edit` - Edit user

### API Endpoints (for token usage)
- `GET /api/stories` - Get stories
- `GET /api/stories/{id}` - Get specific story
- `POST /api/sanity/article/upsert` - Create/update article (write access)
- `POST /api/sanity/article/delete` - Delete article (write access)
- `GET /api/user` - Get current user info

## Navigation Features

### Main Navigation
- Dashboard link for all users
- API Tokens link for approved users
- Users link for administrators
- Pending approvals counter for admins

### User Dropdown Menu
- Profile management
- API token access (if approved)
- Admin functions (if admin)
- Pending approvals with count badge

## Security Features

### Role-Based Access Control
- **Admin**: Full system access, user management
- **Editor**: Content management, API write access
- **User**: Basic API read access

### User Approval System
- New registrations require admin approval
- Unapproved users have limited access
- Approval tracking with notes and timestamps

### API Token Security
- Tokens shown only once during creation
- Role-based token abilities
- Easy revocation and management
- Usage tracking and monitoring

## Customization Options

### 1. User Roles
Modify roles in the User model and update validation rules:
```php
// In User model
public function isCustomRole(): bool
{
    return $this->role === 'custom';
}
```

### 2. Token Abilities
Customize token abilities in `TokenController`:
```php
private function getAbilitiesForUser($user, $requestedAbilities = []): array
{
    // Add custom logic here
}
```

### 3. Approval Workflow
Customize approval process in User model:
```php
public function approve(User $approver, string $notes = null): bool
{
    // Add custom approval logic
    // Send notifications, etc.
}
```

## Troubleshooting

### Common Issues

1. **Users can't access API tokens**
   - Check if user is approved (`is_approved = true`)
   - Verify middleware is working correctly

2. **Admin can't see pending users**
   - Ensure user has admin role
   - Check middleware configuration

3. **API tokens not working**
   - Verify Sanctum configuration
   - Check token abilities match user role
   - Ensure API routes use correct middleware

### Debug Commands

```bash
# Check user status
php artisan tinker
>>> \App\Models\User::find(1)->isApproved()

# Check routes
php artisan route:list --path=admin
php artisan route:list --path=tokens

# Clear cache
php artisan cache:clear
php artisan config:clear
php artisan route:clear
```

## Next Steps

### Recommended Enhancements

1. **Email Notifications**
   - Welcome emails for new users
   - Approval/rejection notifications
   - Token creation alerts

2. **Activity Logging**
   - User login tracking
   - API usage monitoring
   - Admin action logging

3. **Advanced Filtering**
   - Date range filters
   - Advanced search options
   - Export functionality

4. **API Rate Limiting**
   - Per-user rate limits
   - Token-specific limits
   - Usage analytics

### Integration Tips

1. **Frontend Integration**
   - Use API tokens for SPA authentication
   - Implement token refresh logic
   - Handle approval status in frontend

2. **Mobile Apps**
   - Use token-based authentication
   - Implement proper error handling
   - Cache user permissions

3. **Third-party Services**
   - Create service-specific tokens
   - Monitor usage and limits
   - Implement webhook notifications

## Support

The system is now fully functional and ready for production use. All components work together to provide a comprehensive user management solution that integrates seamlessly with your existing API infrastructure.

For additional customization or issues, refer to the Laravel documentation and the specific controller/model implementations in your codebase.
