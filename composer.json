{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0", "guzzlehttp/guzzle": "^7.0.1", "laravel/framework": "^10.0", "laravel/sanctum": "*", "laravel/tinker": "^2.0", "predis/predis": "^2.0", "sanity/sanity-php": "^1.5", "tucker-eric/eloquentfilter": "^3.1"}, "require-dev": {"fakerphp/faker": "^1.24", "laravel/breeze": "*", "laravel/sail": "^1.14", "mockery/mockery": "^1.3.1", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.3", "spatie/laravel-ignition": "^2.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "stable", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}